import { makeAutoObservable } from 'mobx';

export interface ToastMessage {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  duration?: number;
}

export interface ConfirmDialog {
  id: string;
  message: string;
  resolve: (result: boolean) => void;
}

export class NotificationStore {
  toasts: ToastMessage[] = [];
  confirmDialogs: ConfirmDialog[] = [];

  constructor() {
    makeAutoObservable(this);
  }

  // Toast 通知方法
  showSuccess = (message: string, duration = 3000) => {
    this.addToast('success', message, duration);
  };

  showError = (message: string, duration = 5000) => {
    this.addToast('error', message, duration);
  };

  showWarning = (message: string, duration = 4000) => {
    this.addToast('warning', message, duration);
  };

  showInfo = (message: string, duration = 3000) => {
    this.addToast('info', message, duration);
  };

  private addToast = (type: ToastMessage['type'], message: string, duration: number) => {
    const id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
    const toast: ToastMessage = { id, type, message, duration };
    
    this.toasts.push(toast);

    // 自动移除 toast
    if (duration > 0) {
      setTimeout(() => {
        this.removeToast(id);
      }, duration);
    }
  };

  removeToast = (id: string) => {
    this.toasts = this.toasts.filter(toast => toast.id !== id);
  };

  clearAllToasts = () => {
    this.toasts = [];
  };

  // 确认对话框方法
  showConfirm = (message: string): Promise<boolean> => {
    return new Promise((resolve) => {
      const id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
      const dialog: ConfirmDialog = {
        id,
        message,
        resolve
      };
      
      this.confirmDialogs.push(dialog);
    });
  };

  resolveConfirm = (id: string, result: boolean) => {
    const dialog = this.confirmDialogs.find(d => d.id === id);
    if (dialog) {
      dialog.resolve(result);
      this.confirmDialogs = this.confirmDialogs.filter(d => d.id !== id);
    }
  };

  cancelConfirm = (id: string) => {
    this.resolveConfirm(id, false);
  };
}
