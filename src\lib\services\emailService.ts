import { prisma } from '@/lib/db'
import { EmailTemplateType, EmailStatus } from 'generated-prisma'

export interface EmailData {
  to: string[]
  cc?: string[]
  subject: string
  body: string
  templateId?: number
  reportId?: number
}

export interface TemplateVariables {
  user: {
    name: string
    email: string
  }
  project?: {
    projectCode: string
    projectName: string
    studyTitle?: string
    pvEmail?: string
    sponsor?: string
  }
  extracted?: {
    protocolNumber?: string
    subjectNumber?: string 
    eventName?: string
    learnedDate?: string
    seriousness?: string
    causality?: string 
  }
  task?: {
    qcUser?: string
    deUser?: string
    processor?: string
    day0?: string  // 任务创建日期（中文格式）
    day1?: string  // 任务创建日期的第二天（中文格式）
  }
  report?: {
    reportType: string
    status: string
    createdAt: string
  }
}

export class EmailService {
  /**
   * 渲染邮件模板
   */
  static renderTemplate(
    template: string, 
    variables: TemplateVariables, 
    variableMapping?: any
  ): string {
    let rendered = template

    // 替换用户变量
    if (variables.user) {
      rendered = rendered.replace(/\{\{user_name\}\}/g, variables.user.name)
      rendered = rendered.replace(/\{\{user_email\}\}/g, variables.user.email)
    }

    // 替换项目变量
    if (variables.project) {
      rendered = rendered.replace(/\{\{project_code\}\}/g, variables.project.projectCode || '')
      rendered = rendered.replace(/\{\{project_name\}\}/g, variables.project.projectName || '')
      rendered = rendered.replace(/\{\{study_title\}\}/g, variables.project.studyTitle || '')
      rendered = rendered.replace(/\{\{pv_email\}\}/g, variables.project.pvEmail || '')
      rendered = rendered.replace(/\{\{sponsor_contact\}\}/g, variables.project.sponsor || '')
    }

    // 替换提取的信息变量
    if (variables.extracted) {
      rendered = rendered.replace(/\{\{protocol_number\}\}/g, variables.extracted.protocolNumber || '')
      rendered = rendered.replace(/\{\{subject_number\}\}/g, variables.extracted.subjectNumber || '') 
      rendered = rendered.replace(/\{\{event_name\}\}/g, variables.extracted.eventName || '')
      rendered = rendered.replace(/\{\{learned_date\}\}/g, variables.extracted.learnedDate || '')
      rendered = rendered.replace(/\{\{seriousness\}\}/g, variables.extracted.seriousness || '')
      rendered = rendered.replace(/\{\{causality\}\}/g, variables.extracted.causality || '') 
    }
    if(variables.task){
      rendered = rendered.replace(/\{\{processor_user\}\}/g, variables.task.processor || '')
      rendered = rendered.replace(/\{\{qc_user\}\}/g, variables.task.qcUser || '')
      rendered = rendered.replace(/\{\{de_user\}\}/g, variables.task.deUser || '')
      rendered = rendered.replace(/\{\{day0\}\}/g, variables.task.day0 || '')
      rendered = rendered.replace(/\{\{day1\}\}/g, variables.task.day1 || '')
    }

    // 替换报告变量
    if (variables.report) {
      const reportTypeText = this.getReportTypeText(variables.report.reportType)
      rendered = rendered.replace(/\{\{report_type\}\}/g, variables.report.reportType)
      rendered = rendered.replace(/\{\{report_type_text\}\}/g, reportTypeText)
      rendered = rendered.replace(/\{\{report_status\}\}/g, variables.report.status)
      rendered = rendered.replace(/\{\{report_created_at\}\}/g, variables.report.createdAt)
    }

    return rendered
  }

  /**
   * 确定邮件收件人
   */
  static async determineRecipients(
    template: any,
    variables: TemplateVariables,
  ): Promise<{ to: string[], cc: string[] }> {
    const recipients = { to: [] as string[], cc: [] as string[] }

    // 根据模板类型确定收件人
    switch (template.templateType) {
      case 'CONFIRMATION':
        // 确认回复发送给原报告者（通常是中心）
        if (variables.project?.pvEmail) {
          recipients.to.push(variables.project.pvEmail)
        }
        break

      case 'SAFETY_NOTIFICATION':
        // 安全性通知发送给申办方
        if (variables.project?.sponsor) {
          // 这里应该根据申办方查找对应的邮箱
          // 暂时使用项目PV邮箱
          if (variables.project.pvEmail) {
            recipients.to.push(variables.project.pvEmail)
          }
        }
        break

      case 'INTERNAL_FORWARD':
        // 内部转发给项目组成员
        if (template.projectId) {
          const projectUsers = await prisma.userProject.findMany({
            where: { 
              projectId: template.projectId,
              roleInProject: { in: ['DE', 'QC'] }
            },
            include: { user: true }
          })
          
          recipients.to.push(...projectUsers.map(up => up.user.email))
        }
        break

      case 'WM_FORWARD':
        // WM转发给Triage
        const triageUsers = await prisma.user.findMany({
          where: {
            roles: { path: '$', array_contains: 'Triage' },
            isActive: true
          }
        })
        recipients.to.push(...triageUsers.map(u => u.email))
        break
    }

    // 处理模板配置的收件人
    if (template.recipientEmails && template.recipientEmails.length > 0) {
      recipients.to.push(...template.recipientEmails)
    }

    // 处理抄送
    if (template.ccEmails && template.ccEmails.length > 0) {
      recipients.cc.push(...template.ccEmails)
    }

    return recipients
  }
 
  /**
   * 根据模板ID获取邮件类型
   */
  private static async getEmailTypeFromTemplate(templateId?: number): Promise<EmailTemplateType> {
    if (!templateId) return 'CONFIRMATION'
    
    try {
      const template = await prisma.emailTemplate.findUnique({
        where: { id: templateId },
        select: { templateType: true }
      })
      
      return template?.templateType || 'CONFIRMATION'
    } catch {
      return 'CONFIRMATION'
    }
  }

  /**
   * 获取报告类型文本
   */
  private static getReportTypeText(reportType: string): string {
    switch (reportType) {
      case 'INITIAL':
        return '首次报告'
      case 'FOLLOW_UP':
        return '随访报告'
      case 'QUERY_RESPONSE':
        return '质疑回复'
      default:
        return reportType
    }
  }

  /**
   * 生成邮件内容
   */
  static async generateEmailContent(
    templateId: number,
    variables: TemplateVariables
  ): Promise<{ subject: string, body: string, recipients: { to: string[], cc: string[] } }> {
    const template = await prisma.emailTemplate.findUnique({
      where: { id: templateId },
      include: {
        project: true
      }
    })

    if (!template) {
      throw new Error('邮件模板不存在')
    }

    // 渲染主题
    const subject = template.subjectTemplate
      ? this.renderTemplate(template.subjectTemplate, variables, template.variableMapping)
      : '系统通知'

    // 渲染正文
    const body = this.renderTemplate(
      template.bodyTemplate,
      variables,
      template.variableMapping
    )

    // 确定收件人
    const recipients = await this.determineRecipients(template, variables)

    return { subject, body, recipients }
  }
}
