import {
  ExchangeService,
  ExchangeVersion,
  WebCredentials,
  Uri,
  WellKnownFolderName,
  ItemView,
  ItemId,
  EmailMessage,
  PropertySet,
  BasePropertySet,
  EmailMessageSchema,
  FileAttachment,
  ItemAttachment,
  EmailAddressCollection
} from 'ews-javascript-api'

export interface EmailContent {
  id: string
  subject: string
  sender: string
  recipients: string[]
  ccRecipients: string[]
  body: string
  htmlBody: string
  receivedTime: Date
  attachments: EmailAttachment[]
  metadata: {
    messageId: string
    importance: string
    hasAttachments: boolean
  }
}

export interface EmailAttachment {
  name: string
  contentType: string
  size: number
  content: Buffer
  isInline: boolean
}

/**
 * 增强版重试配置
 */
interface RetryConfig {
  maxRetries: number
  initialDelay: number
  maxDelay: number
  backoffMultiplier: number
  jitter: boolean
}

/**
 * 默认重试配置
 */
const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 10,
  initialDelay: 1000,
  maxDelay: 30000,
  backoffMultiplier: 2,
  jitter: true
}

/**
 * 重试工具函数
 */
async function retryWithBackoff<T>(
  operation: () => Promise<T>,
  config: RetryConfig = DEFAULT_RETRY_CONFIG,
  operationName: string = 'EWS操作'
): Promise<T> {
  let lastError: Error | null = null
  
  for (let attempt = 1; attempt <= config.maxRetries + 1; attempt++) {
    try {
      console.log(`🔄 ${operationName} 尝试 ${attempt}/${config.maxRetries + 1}`)
      const result = await operation()
      console.log(`✅ ${operationName} 成功 (第${attempt}次尝试)`)
      return result

    } catch (error) {
      lastError = error as Error

      // 如果是最后一次尝试，直接抛出错误
      if (attempt === config.maxRetries + 1) {
        console.error(`💥 ${operationName} 最终失败，已尝试${config.maxRetries + 1}次:`, lastError.message)
        throw error
      }
      
      // 计算退避时间
      let delay = config.initialDelay * Math.pow(config.backoffMultiplier, attempt - 1)
      delay = Math.min(delay, config.maxDelay)
      
      // 添加抖动（随机化）避免惊群效应
      if (config.jitter) {
        delay = delay * (0.5 + Math.random() * 0.5)
      }
      
      console.warn(`❌ ${operationName} 失败 (第${attempt}次):`, lastError.message)
      console.log(`⏳ ${operationName} ${Math.round(delay)}ms后进行第${attempt + 1}次重试...`)
      
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
  
  throw lastError || new Error(`${operationName} 重试逻辑异常`)
}

export class EWSMailService {
  private service: ExchangeService | null = null
  private isConnected = false
  private retryConfig: RetryConfig

  constructor(retryConfig: Partial<RetryConfig> = {}) {
    this.retryConfig = { ...DEFAULT_RETRY_CONFIG, ...retryConfig }
  }

  /**
   * 连接到EWS服务（移除测试连接，延迟到实际调用时）
   */
  async connect(domainAccount: string, password: string): Promise<boolean> {
    this.service = new ExchangeService(ExchangeVersion.Exchange2016)
    this.service.Credentials = new WebCredentials(domainAccount, password)
    this.service.Url = new Uri("https://owa.pharmaron-bj.com/EWS/Exchange.asmx")
    
    // 不再测试连接，延迟到实际调用时
    this.isConnected = true
    return true
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    this.service = null
    this.isConnected = false
  }
 
  

  /**
   * 获取连接状态
   */
  isServiceConnected(): boolean {
    return this.isConnected && this.service !== null
  }

  /**
   * 更新重试配置
   */
  updateRetryConfig(config: Partial<RetryConfig>): void {
    this.retryConfig = { ...this.retryConfig, ...config }
  }

  /**
   * 获取当前重试配置
   */
  getRetryConfig(): RetryConfig {
    return { ...this.retryConfig }
  }
}

// 导出单例实例（使用默认配置）
export const ewsMailService = new EWSMailService()

// 导出可配置实例创建函数
export const createEWSMailService = (config: Partial<RetryConfig> = {}) => {
  return new EWSMailService(config)
}
