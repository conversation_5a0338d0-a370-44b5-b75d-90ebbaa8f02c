import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { LogIn, RefreshCw } from 'lucide-react';

interface LoginFormProps {
  loginEmail: string;
  loginPassword: string;
  isLoggingIn: boolean;
  onPasswordChange: (password: string) => void;
  onLogin: () => void;
}

export function LoginForm({
  loginEmail,
  loginPassword,
  isLoggingIn,
  onPasswordChange,
  onLogin
}: LoginFormProps) {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onLogin();
  };

  return (
    <div className="p-4 h-full flex items-center justify-center">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-center">登录 Triage 系统</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="email" className="block text-sm font-medium mb-1">
                邮箱地址
              </label>
              <Input
                id="email"
                type="email"
                value={loginEmail}
                readOnly
                placeholder="请输入邮箱地址"
                disabled={isLoggingIn}
                required
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium mb-1">
                密码
              </label>
              <Input
                id="password"
                type="password"
                value={loginPassword}
                onChange={(e) => onPasswordChange(e.target.value)}
                placeholder="请输入密码"
                disabled={isLoggingIn}
                required
              />
            </div>

            <Button
              type="submit"
              disabled={isLoggingIn || !loginEmail || !loginPassword}
              className="w-full"
            >
              {isLoggingIn ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  登录中...
                </>
              ) : (
                <>
                  <LogIn className="h-4 w-4 mr-2" />
                  登录
                </>
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
