// 导出所有 stores
export { NotificationStore } from './NotificationStore';
export { OfficeStore } from './OfficeStore';
export { AuthStore } from './AuthStore';
export { ProjectStore } from './ProjectStore';
export { EmailStore } from './EmailStore';
export { RootStore } from './RootStore';

// 导出 Provider 和 hooks
export {
  StoreProvider,
  useStores,
  useNotificationStore,
  useOfficeStore,
  useAuthStore,
  useProjectStore,
  useEmailStore
} from './StoreProvider';

// 导出类型
export type { ToastMessage, ConfirmDialog } from './NotificationStore';
