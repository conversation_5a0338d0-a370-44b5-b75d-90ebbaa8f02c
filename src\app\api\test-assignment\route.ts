import { NextRequest, NextResponse } from 'next/server'
import { TaskProcessorService } from '@/lib/services/taskProcessorService'
import { requireAuth } from '@/lib/auth'
import { UserRole } from 'generated-prisma'

/**
 * 测试人员分配逻辑的API端点
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户权限 - 只有WM和PM角色可以测试
    const authResult = await requireAuth(request, [UserRole.WM, UserRole.PM])
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const body = await request.json()
    const { projectId } = body

    console.log(`🧪 用户 ${authResult.user?.name} 测试人员分配逻辑`)

    const taskProcessorService = new TaskProcessorService()
    
    // 调用私有方法进行测试（仅用于测试目的）
    const assignedPersonnel = await (taskProcessorService as any).assignPersonnel(projectId)

    return NextResponse.json({
      success: true,
      message: '人员分配测试完成',
      data: {
        assignedPersonnel: {
          processor: assignedPersonnel.processor ? {
            id: assignedPersonnel.processor.id,
            name: assignedPersonnel.processor.name,
            email: assignedPersonnel.processor.email
          } : null,
          dataEntry: assignedPersonnel.dataEntry ? {
            id: assignedPersonnel.dataEntry.id,
            name: assignedPersonnel.dataEntry.name,
            email: assignedPersonnel.dataEntry.email
          } : null,
          qualityControl: assignedPersonnel.qualityControl ? {
            id: assignedPersonnel.qualityControl.id,
            name: assignedPersonnel.qualityControl.name,
            email: assignedPersonnel.qualityControl.email
          } : null
        }
      }
    })

  } catch (error) {
    console.error('人员分配测试失败:', error)
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : '未知错误' 
      },
      { status: 500 }
    )
  }
}

/**
 * 获取用户工作量统计信息
 */
export async function GET(request: NextRequest) {
  try {
    // 验证用户权限
    const authResult = await requireAuth(request, [UserRole.WM, UserRole.PM])
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const { searchParams } = new URL(request.url)
    const projectId = searchParams.get('projectId')

    const taskProcessorService = new TaskProcessorService()
    
    // 获取用户工作量统计
    const workloadStats = await (taskProcessorService as any).getUserWorkloadStats(
      projectId ? parseInt(projectId) : undefined
    )

    return NextResponse.json({
      success: true,
      data: workloadStats
    })

  } catch (error) {
    console.error('获取工作量统计失败:', error)
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : '未知错误' 
      },
      { status: 500 }
    )
  }
}
