'use client';
import { makeAutoObservable, runInAction } from 'mobx';
import { UserInfo } from '../types';
import type { NotificationStore } from './NotificationStore';
import type { ProjectStore } from './ProjectStore';

export class AuthStore {
  userInfo: UserInfo | null = null;
  isLoggingIn = false;
  loginPassword = '';

  private notificationStore: NotificationStore;
  private projectStore: ProjectStore;

  constructor(notificationStore: NotificationStore, projectStore: ProjectStore) {
    makeAutoObservable(this);
    this.notificationStore = notificationStore;
    this.projectStore = projectStore;
  }

  // 获取登录邮箱（从 Office.js 获取）
  get loginEmail(): string {
    if (Office.context.mailbox.userProfile) {
      return Office.context.mailbox.userProfile.emailAddress || '';
    }
    return '';
  }

  // 设置登录密码
  setLoginPassword = (password: string) => {
    this.loginPassword = password;
  };

  // 检查认证状态
  checkAuthStatus = async () => {
    try {
      const response = await fetch('/api/auth/identify', {
        method: 'GET',
        credentials: 'include'
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data.user) {
          const user = result.data.user;
          const userInfo: UserInfo = {
            email: user.email,
            displayName: user.name,
            roles: user.roles || [],
            department: user.department,
            isLoggedIn: true,
          };
          runInAction(() => {
            this.userInfo = userInfo;
          })
          
          // 加载项目列表
          await this.projectStore.loadProjects();
        }
      }
    } catch (error) {
      console.error('检查认证状态失败:', error);
      // 如果检查失败，不显示错误，让用户正常登录
    }
  };

  // 登录处理
  handleLogin = async () => {
    if (!this.loginEmail) {
      this.notificationStore.showWarning('请输入邮箱地址');
      return;
    }

    if (!this.loginPassword) {
      this.notificationStore.showWarning('请输入密码');
      return;
    }

    this.isLoggingIn = true;
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: this.loginEmail,
          password: this.loginPassword,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        const user = result.user;
        const userInfo: UserInfo = {
          email: user.mail,
          displayName: user.displayName || user.name,
          roles: user.roles || [],
          department: user.department,
          isLoggedIn: true,
        };
        this.userInfo = userInfo;
        await this.projectStore.loadProjects();
        this.notificationStore.showSuccess('登录成功');
      } else {
        this.notificationStore.showError(`登录失败: ${result.error}`);
      }
    } catch (error) {
      console.error('登录失败:', error);
      this.notificationStore.showError('登录过程中发生错误，请稍后重试');
    } finally {
      this.isLoggingIn = false;
    }
  };

  // 退出登录
  handleLogout = async () => {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include'
      });
      
      // 清除本地状态
      this.userInfo = null;
      this.loginPassword = '';
      this.projectStore.clearProjects();
      this.notificationStore.showSuccess('已成功退出登录');
    } catch (error) {
      console.error('退出登录失败:', error);
      this.notificationStore.showError('退出登录失败，请重试');
    }
  };
}
