import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/Select';
import { 
  CheckCircle, 
  Clock, 
  AlertCircle, 
  XCircle, 
  RefreshCw, 
  Filter,
  Mail,
  User,
  Calendar,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import type { Prisma } from 'generated-prisma';
import type { TaskMetadata } from '@/types/task';
import dayjs from 'dayjs'

type _Task = Prisma.TaskGetPayload<{
  include: {
    project: true;
    assignedTo: true;
    qcTo: true;
    deTo: true;
    createdBy: true;
  }
}>

type Task =  Omit<_Task,"metadata"> & {
  metadata: TaskMetadata
}
 
export function TaskList() {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState({
    status: '',
    priority: '',
    assignedToMe: false,
    createdByMe: false
  });
  const [showFilters, setShowFilters] = useState(false);
  const [expandedTasks, setExpandedTasks] = useState<Set<number>>(new Set());

  // 加载任务列表
  const loadTasks = async () => {
    setIsLoading(true);
    setError('');
    
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10'
      });

      if (filters.status) params.append('status', filters.status);
      if (filters.priority) params.append('priority', filters.priority);
      if (filters.assignedToMe) params.append('assignedToMe', 'true');
      if (filters.createdByMe) params.append('createdByMe', 'true');

      const response = await fetch(`/api/tasks?${params.toString()}`, {
        credentials: 'include'
      });

      const data = await response.json();

      if (data.success) {
        setTasks(data.tasks);
        setTotalPages(data.pagination.totalPages);
      } else {
        setError(data.error || '加载任务失败');
      }
    } catch (err) {
      console.error('加载任务失败:', err);
      setError('加载任务失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadTasks();
  }, [page, filters]);

  // 获取状态图标和颜色
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'PENDING':
        return { icon: Clock, color: 'bg-yellow-100 text-yellow-800', text: '待处理' };
      case 'IN_PROGRESS':
        return { icon: AlertCircle, color: 'bg-blue-100 text-blue-800', text: '处理中' };
      case 'COMPLETED':
        return { icon: CheckCircle, color: 'bg-green-100 text-green-800', text: '已完成' };
      case 'CANCELLED':
        return { icon: XCircle, color: 'bg-red-100 text-red-800', text: '已取消' };
      default:
        return { icon: Clock, color: 'bg-gray-100 text-gray-800', text: status };
    }
  };

  // 获取优先级颜色
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT':
        return 'bg-red-100 text-red-800';
      case 'HIGH':
        return 'bg-orange-100 text-orange-800';
      case 'MEDIUM':
        return 'bg-blue-100 text-blue-800';
      case 'LOW':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 获取优先级文本
  const getPriorityText = (priority: string) => {
    switch (priority) {
      case 'URGENT': return '紧急';
      case 'HIGH': return '高';
      case 'MEDIUM': return '中';
      case 'LOW': return '低';
      default: return priority;
    }
  };

  // 格式化时间
  const formatDate = (dateString: string | Date) => {
    return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss');
  };

  // 切换任务展开状态
  const toggleTaskExpanded = (taskId: number) => {
    const newExpanded = new Set(expandedTasks);
    if (newExpanded.has(taskId)) {
      newExpanded.delete(taskId);
    } else {
      newExpanded.add(taskId);
    }
    setExpandedTasks(newExpanded);
  };

  // 更新任务状态
  const updateTaskStatus = async (taskId: number, newStatus: string) => {
    try {
      const response = await fetch('/api/tasks', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({
          taskId,
          status: newStatus
        })
      });

      const data = await response.json();

      if (data.success) {
        // 重新加载任务列表
        loadTasks();
      } else {
        setError(data.error || '更新任务状态失败');
      }
    } catch (err) {
      console.error('更新任务状态失败:', err);
      setError('更新任务状态失败，请重试');
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
          <p className="text-sm text-muted-foreground">加载任务列表中...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-sm">
            <CheckCircle className="h-4 w-4" />
            任务列表
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              onClick={() => setShowFilters(!showFilters)}
              variant="outline"
              size="sm"
            >
              <Filter className="h-3 w-3 mr-1" />
              筛选
              {showFilters ? <ChevronUp className="h-3 w-3 ml-1" /> : <ChevronDown className="h-3 w-3 ml-1" />}
            </Button>
            <Button
              onClick={loadTasks}
              variant="outline"
              size="sm"
            >
              <RefreshCw className="h-3 w-3 mr-1" />
              刷新
            </Button>
          </div>
        </div>

        {/* 筛选器 */}
        {showFilters && (
          <div className="mt-3 p-3 bg-muted/30 rounded-lg space-y-3">
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="text-xs font-medium mb-1 block">状态</label>
                <Select
                  value={filters.status}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, status: value === '' ? '' : value }))}
                >
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue placeholder="全部状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">全部状态</SelectItem>
                    <SelectItem value="PENDING">待处理</SelectItem>
                    <SelectItem value="IN_PROGRESS">处理中</SelectItem>
                    <SelectItem value="COMPLETED">已完成</SelectItem>
                    <SelectItem value="CANCELLED">已取消</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-xs font-medium mb-1 block">优先级</label>
                <Select
                  value={filters.priority}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, priority: value === '' ? '' : value }))}
                >
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue placeholder="全部优先级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">全部优先级</SelectItem>
                    <SelectItem value="URGENT">紧急</SelectItem>
                    <SelectItem value="HIGH">高</SelectItem>
                    <SelectItem value="MEDIUM">中</SelectItem>
                    <SelectItem value="LOW">低</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                onClick={() => setFilters(prev => ({ ...prev, assignedToMe: !prev.assignedToMe }))}
                variant={filters.assignedToMe ? "default" : "outline"}
                size="sm"
                className="text-xs"
              >
                分配给我的
              </Button>
              <Button
                onClick={() => setFilters(prev => ({ ...prev, createdByMe: !prev.createdByMe }))}
                variant={filters.createdByMe ? "default" : "outline"}
                size="sm"
                className="text-xs"
              >
                我创建的
              </Button>
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent className="space-y-3">
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {tasks.length === 0 ? (
          <div className="text-center py-6">
            <p className="text-sm text-muted-foreground">暂无任务</p>
          </div>
        ) : (
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {tasks.map((task) => {
              const statusInfo = getStatusInfo(task.status);
              const StatusIcon = statusInfo.icon;
              const isExpanded = expandedTasks.has(task.id);

              return (
                <div
                  key={task.id}
                  className="border rounded-lg p-3 bg-white hover:bg-muted/30 transition-colors"
                >
                  {/* 任务基本信息 */}
                  <div className="flex items-start justify-between gap-2">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="text-sm font-medium truncate">
                          {task.title}
                        </h4>
                        <Badge className={`text-xs ${statusInfo.color}`}>
                          <StatusIcon className="h-3 w-3 mr-1" />
                          {statusInfo.text}
                        </Badge>
                        <Badge className={`text-xs ${getPriorityColor(task.priority)}`}>
                          {getPriorityText(task.priority)}
                        </Badge>
                      </div>
                      
                      {task.metadata.subject && (
                        <div className="flex items-center gap-1 text-xs text-muted-foreground mb-1">
                          <Mail className="h-3 w-3" />
                          <span className="truncate">{task.metadata.subject}</span>
                        </div>
                      )}
                      
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <Calendar className="h-3 w-3" />
                        <span>{formatDate(task.createdAt)}</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-1">
                      {task.status === 'PENDING' && (
                        <Button
                          onClick={() => updateTaskStatus(task.id, 'IN_PROGRESS')}
                          size="sm"
                          variant="outline"
                          className="text-xs h-6 px-2"
                        >
                          开始处理
                        </Button>
                      )} 
                      <Button
                        onClick={() => toggleTaskExpanded(task.id)}
                        variant="default"
                        size="sm"
                        className="h-6 w-6 p-0"
                      >
                        {isExpanded ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />}
                      </Button>
                    </div>
                  </div>

                  {/* 展开的详细信息 */}
                  {isExpanded && (
                    <div className="mt-3 pt-3 border-t space-y-2">
                      {task.description && (
                        <div>
                          <label className="text-xs font-medium text-muted-foreground">描述</label>
                          <p className="text-xs mt-1">{task.description}</p>
                        </div>
                      )}
                      
                      {task.metadata.sender && (
                        <div>
                          <label className="text-xs font-medium text-muted-foreground">邮件发件人</label>
                          <p className="text-xs mt-1">{task.metadata.sender}</p>
                        </div>
                      )}
                      
                      
                      {task.project && (
                        <div>
                          <label className="text-xs font-medium text-muted-foreground">项目</label>
                          <p className="text-xs mt-1">{task.project.projectCode} - {task.project.projectName}</p>
                        </div>
                      )}
                      
                      <div className="grid grid-cols-2 gap-2">
                        {task.assignedTo && (
                          <div>
                            <label className="text-xs font-medium text-muted-foreground">处理人</label>
                            <p className="text-xs mt-1">{task.assignedTo.name}</p>
                          </div>
                        )}
                        
                        {task.qcTo && (
                          <div>
                            <label className="text-xs font-medium text-muted-foreground">QC</label>
                            <p className="text-xs mt-1">{task.qcTo.name}</p>
                          </div>
                        )}
                      </div>
                      
                      <div>
                        <label className="text-xs font-medium text-muted-foreground">创建人</label>
                        <p className="text-xs mt-1">{task.createdBy.name}</p>
                      </div>
                      
                      {task.completedAt && (
                        <div>
                          <label className="text-xs font-medium text-muted-foreground">完成时间</label>
                          <p className="text-xs mt-1">{formatDate(task.completedAt)}</p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}

        {/* 分页 */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between pt-3 border-t">
            <Button
              onClick={() => setPage(prev => Math.max(1, prev - 1))}
              disabled={page === 1}
              variant="outline"
              size="sm"
            >
              上一页
            </Button>
            <span className="text-xs text-muted-foreground">
              第 {page} 页，共 {totalPages} 页
            </span>
            <Button
              onClick={() => setPage(prev => Math.min(totalPages, prev + 1))}
              disabled={page === totalPages}
              variant="outline"
              size="sm"
            >
              下一页
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
