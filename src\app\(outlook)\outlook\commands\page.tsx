'use client';

import { useEffect } from 'react'; 

declare global {
  interface Window {
    Office: any;
  }
}

export default function OutlookCommands() {
  useEffect(() => {
    // 确保Office.js已加载
    if (typeof window !== 'undefined' && window.Office) {
      window.Office.onReady(() => {
        // 注册自动分发函数
        window.Office.actions.associate('autoDistribute', autoDistribute);
      });
    }
  }, []);

  // 自动识别分发函数
  const autoDistribute = async (event: any) => {
    try {
      // 获取当前选中的邮件
      const mailbox = window.Office.context.mailbox;
      
      // 显示处理中状态
      window.Office.context.ui.displayDialogAsync(
        window.location.origin + '/outlook/processing',
        { height: 30, width: 40 }
      );

      // 获取选中邮件的信息
      const selectedItems = await getSelectedEmails();
      
      if (selectedItems.length === 0) {
        showNotification('请先选择要处理的邮件');
        event.completed();
        return;
      }

      // 调用后端API进行自动分发
      const response = await fetch('/api/outlook/auto-distribute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          emails: selectedItems,
          userEmail: mailbox.userProfile.emailAddress,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        showNotification(`成功处理 ${result.processedCount} 封邮件`);
      } else {
        showNotification(`处理失败: ${result.error}`);
      }
    } catch (error) {
      console.error('自动分发处理失败:', error);
      showNotification('处理过程中发生错误，请稍后重试');
    } finally {
      // 完成事件处理
      event.completed();
    }
  };

  // 获取选中的邮件信息
  const getSelectedEmails = (): Promise<any[]> => {
    return new Promise((resolve) => {
      // 这里需要根据实际的Office.js API来获取选中的邮件
      // 由于当前版本的限制，我们先返回当前邮件的信息
      const currentItem = window.Office.context.mailbox.item;
      if (currentItem) {
        const emailInfo = {
          subject: currentItem.subject,
          sender: currentItem.from,
          recipients: currentItem.to,
          body: currentItem.body,
          itemId: currentItem.itemId,
          dateTimeCreated: currentItem.dateTimeCreated,
        };
        resolve([emailInfo]);
      } else {
        resolve([]);
      }
    });
  };

  // 显示通知
  const showNotification = (message: string) => {
    if (window.Office && window.Office.context.ui) {
      window.Office.context.ui.displayDialogAsync(
        `data:text/html,<html><body style="font-family:Arial;padding:20px;text-align:center;">${message}</body></html>`,
        { height: 20, width: 40 },
        (result: any) => {
          if (result.status === 'succeeded') {
            setTimeout(() => {
              result.value.close();
            }, 3000);
          }
        }
      );
    }
  };

  return (
    <div style={{ display: 'none' }}>  
    </div>
  );
}
