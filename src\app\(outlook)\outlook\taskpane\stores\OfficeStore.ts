'use client';
import { makeAutoObservable, runInAction } from 'mobx';
import { loadOfficeJs } from '../helper';

export class OfficeStore {
  isOfficeReady = false;
  isLoading = true;
  error: string | null = null;

  constructor() {
    makeAutoObservable(this);
    // this.initializeOffice();
  }

  // 初始化 Office.js
  initializeOffice = async () => {
    try {
      this.isLoading = true;
      this.error = null;
      
      await loadOfficeJs();
      
      window.Office.onReady(() => { 
        // 获取当前邮箱信息 
        Office.context.mailbox.addHandlerAsync(
          Office.EventType.ItemChanged,
          function () {
            // const item = Office.context.mailbox.item;
            // console.log('item :>> ', item);
          },
          function (asyncResult) {
            if (asyncResult.status === Office.AsyncResultStatus.Succeeded) {
              console.log("ItemChanged handler added successfully.");
            } else {
              console.error("Failed to add ItemChanged handler.", asyncResult.error);
            }
          }
        ); 
        runInAction(() => {
          this.isOfficeReady = true;
          this.isLoading = false;
        })
      });
    } catch (error) {
      console.error('Failed to load Office.js:', error);
      const errorMessage = 'Office.js加载失败,请右键刷新页面重试';
      this.error = errorMessage;
      this.isLoading = false;
    }
  };

  // 获取当前用户邮箱
  getCurrentUserEmail = (): string => {
    if (window.Office && Office.context.mailbox.userProfile) {
      return Office.context.mailbox.userProfile.emailAddress || '';
    }
    return '';
  };

  // 重置错误状态
  clearError = () => {
    this.error = null;
  };
}
