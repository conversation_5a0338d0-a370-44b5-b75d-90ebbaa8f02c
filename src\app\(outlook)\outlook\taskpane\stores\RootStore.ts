import { NotificationStore } from './NotificationStore';
import { OfficeStore } from './OfficeStore';
import { AuthStore } from './AuthStore';
import { ProjectStore } from './ProjectStore';
import { EmailStore } from './EmailStore';

export class RootStore {
  notificationStore: NotificationStore;
  officeStore: OfficeStore;
  authStore: AuthStore;
  projectStore: ProjectStore;
  emailStore: EmailStore;

  constructor() {
    // 创建通知 store
    this.notificationStore = new NotificationStore();
    
    // 创建 Office.js store
    this.officeStore = new OfficeStore();
    
    // 创建项目 store
    this.projectStore = new ProjectStore(this.notificationStore);
    
    // 创建认证 store
    this.authStore = new AuthStore(this.notificationStore, this.projectStore);
    
    // 创建邮件 store
    this.emailStore = new EmailStore(this.notificationStore);

    // 设置循环依赖
    this.projectStore.setAuthStore(this.authStore);
    this.emailStore.setAuthStore(this.authStore);
  }

  // 初始化应用
  initialize = async () => {
    // 等待 Office.js 准备就绪
    if (this.officeStore.isOfficeReady) {
      await this.authStore.checkAuthStatus();
    }
  };

  // 清理资源
  dispose = () => {
    // 清理所有 stores 的状态
    this.notificationStore.clearAllToasts();
    this.authStore.userInfo = null;
    this.projectStore.clearProjects();
    this.emailStore.clearSelectedEmails();
  };
}
