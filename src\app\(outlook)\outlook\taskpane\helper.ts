'use client';
export const loadOfficeJs = (): Promise<void> => { 
  return new Promise((resolve, reject) => {    
    const script = document.createElement('script')
    script.src = 'https://appsforoffice.cdn.partner.office365.cn/appsforoffice/lib/1/hosted/office.js' 
    script.onload = () => {
      setTimeout(() => {
        resolve();
      },500);
    }
    script.onerror = (error) => {
      reject(error)
    }
    document.head.appendChild(script);
  });
}
 

// 获取当前用户邮箱
export  const getCurrentUserEmail = () => {
  if (window.Office && Office.context.mailbox.userProfile) {
    return Office.context.mailbox.userProfile.emailAddress || '';
  }
  return '';
}

export const convertToEwsId = (itemId: string) => {
  return Office.context.mailbox.convertToEwsId(
    itemId,
    Office.MailboxEnums.RestVersion.v2_0, // 可选 REST 版本 
  );
}


export async function getCurEmailDetails() {
  const item = Office.context.mailbox.item;
  if(!item) throw new Error('未选择邮件');

  const subject = item.subject;
  const from = formatEmailAddress(item.from.displayName, item.from.emailAddress);
  const to = formatAddressList(item.to);
  const cc = formatAddressList(item.cc);
  const date = item.dateTimeCreated.toUTCString();

  const body = await new Promise((resolve, reject) => {
    item.body.getAsync("html", (res) => 
      res.status === Office.AsyncResultStatus.Succeeded
        ? resolve((res.value || '').replace(/<!--\[if.*?endif\]-->/gs, ''))
        : reject(res.error)
    );
  });

  const attachments = await Promise.all(
    (item.attachments || []).map(async (att) => {
      
      const file = await new Promise<Office.AttachmentContent>((resolve, reject) => {
        item.getAttachmentContentAsync(att.id, (res) => 
          res.status === Office.AsyncResultStatus.Succeeded
            ? resolve(res.value)
            : reject(res.error)
        );
      });
 
      return {
        name: att.name,
        size: att.size,
        isInline: att.isInline,
        id: att.id,
        contentType: att.contentType || "application/octet-stream",
        content: file.content, // base64
        encoding: file.format // should be "base64"
      };
    })
  );

  return { from, to, cc, subject, date, body, attachments };
}

function encodeDisplayName(name: string): string {
  if (!name || /^[\x00-\x7F]*$/.test(name)) {
    return name; // ASCII，无需编码
  }

  const utf8 = new TextEncoder().encode(name);
  const base64 = btoa(String.fromCharCode(...utf8));
  return `=?UTF-8?B?${base64}?=`;
}

function formatEmailAddress(displayName: string | undefined, email: string): string {
  if (!displayName || displayName === email) {
    return `<${email}>`;
  }

  const encodedName = encodeDisplayName(displayName);
  return `${encodedName} <${email}>`;
}

function formatAddressList(addresses: { displayName: string; emailAddress: string }[] | undefined): string {
  if (!addresses || addresses.length === 0) return "";
  return addresses
    .map(a => formatEmailAddress(a.displayName, a.emailAddress))
    .join(", ");
}

export function buildMimeEmail({ from, to, cc, subject, date, body, attachments }: Awaited<ReturnType<typeof getCurEmailDetails>> ) {
  const boundary = "----=_NextPart_" + Date.now();
  const headers = [
    `From: ${from}`,
    `To: ${to}`,
    cc ? `Cc: ${cc}` : "",
    `Subject: ${subject}`,
    `Date: ${date}`,
    `MIME-Version: 1.0`,
    `Content-Type: multipart/mixed; boundary="${boundary}"`
  ].filter(Boolean).join("\r\n");

  const parts = [];

  // HTML body
  parts.push([
    `--${boundary}`,
    `Content-Type: text/html; charset="UTF-8"`,
    `Content-Transfer-Encoding: quoted-printable`,
    ``,
    body
  ].join("\r\n"));

  // Attachments
  attachments.forEach(att => {
    parts.push([
      `--${boundary}`,
      `Content-ID: <${att.name}>`,
      `Content-Type: ${att.contentType}; name="${att.name}"`,
      `Content-Transfer-Encoding: base64`,
      `Content-Disposition: ${att.isInline ? 'inline' : 'attachment'}; filename="${att.name}"`,
      ``,
      att.content
    ].join("\r\n"));
  });

  // End boundary
  parts.push(`--${boundary}--`);

  return `${headers}\r\n\r\n${parts.join("\r\n")}`;
}

 


