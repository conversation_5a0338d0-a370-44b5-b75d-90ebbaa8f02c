import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { Mail, User, Building, LogOut } from 'lucide-react';
import { UserInfo } from '../types';

interface UserInfoCardProps {
  userInfo: UserInfo;
  onLogout: () => void;
}

export function UserInfoCard({ userInfo, onLogout }: UserInfoCardProps) {
  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'WM':
        return 'default';
      case 'Triage':
        return 'success';
      default:
        return 'info';
    }
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-sm">
            <User className="h-4 w-4" />
            用户信息
          </CardTitle>
          <Button variant="outline" size="sm" onClick={onLogout}>
            <LogOut className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-2">
        <div className="flex items-center gap-2">
          <Mail className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm text-black">{userInfo.email}</span>
        </div>
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm text-black">{userInfo.displayName}</span>
        </div>
        <div className="flex items-center gap-2 flex-wrap">
          {userInfo.roles.map((role) => (
            <Badge key={role} variant={getRoleBadgeVariant(role)}>
              {role}
            </Badge>
          ))}
          {userInfo.department && (
            <>
              <Building className="h-4 w-4 text-muted-foreground" />
              <Badge variant="info">{userInfo.department}</Badge>
            </>
          )}
        </div>
        {(userInfo.dailyReportMin !== undefined || userInfo.dailyReportMax !== undefined) && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">每日处理报告数量:</span>
            <span className="text-sm text-black">
              {userInfo.dailyReportMin ?? 0} - {userInfo.dailyReportMax ?? 10}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
