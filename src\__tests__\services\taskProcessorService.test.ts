import { TaskProcessorService } from '@/lib/services/taskProcessorService'
import { prisma } from '@/lib/db'
import { UserRole, TaskStatus } from 'generated-prisma'

// Mock prisma
jest.mock('@/lib/db', () => ({
  prisma: {
    task: {
      count: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
    },
    user: {
      findMany: jest.fn(),
      findFirst: jest.fn(),
    },
    userProject: {
      findMany: jest.fn(),
    },
  },
}))

// Mock other dependencies
jest.mock('@/lib/services/aiExtractor', () => ({
  AIExtractor: jest.fn().mockImplementation(() => ({
    extractSAEInfo: jest.fn(),
  })),
}))

jest.mock('@/lib/services/projectService', () => ({
  projectService: {
    matchProject: jest.fn(),
  },
}))

describe('TaskProcessorService - 人员分配测试', () => {
  let taskProcessorService: TaskProcessorService
  const mockPrisma = prisma as jest.Mocked<typeof prisma>

  beforeEach(() => {
    taskProcessorService = new TaskProcessorService()
    jest.clearAllMocks()
  })

  describe('assignUserByWorkload', () => {
    const mockUsers = [
      {
        id: 1,
        name: '张三',
        email: '<EMAIL>',
        roles: [UserRole.Triage],
        dailyReportMin: 3,
        dailyReportMax: 8,
        isActive: true,
      },
      {
        id: 2,
        name: '李四',
        email: '<EMAIL>',
        roles: [UserRole.Triage],
        dailyReportMin: 2,
        dailyReportMax: 10,
        isActive: true,
      },
      {
        id: 3,
        name: '王五',
        email: '<EMAIL>',
        roles: [UserRole.Triage],
        dailyReportMin: 5,
        dailyReportMax: 12,
        isActive: true,
      },
    ]

    it('应该优先分配给未达到最小任务数的用户', async () => {
      // 模拟任务计数：张三2个，李四1个，王五6个
      mockPrisma.task.count
        .mockResolvedValueOnce(2) // 张三
        .mockResolvedValueOnce(1) // 李四
        .mockResolvedValueOnce(6) // 王五

      // 使用反射调用私有方法进行测试
      const result = await (taskProcessorService as any).assignUserByWorkload(
        mockUsers,
        'assignedToId'
      )

      // 李四只有1个任务，未达到最小值2，应该被选中
      expect(result).toEqual(mockUsers[1])
      expect(result.name).toBe('李四')
    })

    it('应该在未达到最小值的用户中选择任务最少的', async () => {
      // 模拟任务计数：张三1个，李四0个，王五6个
      mockPrisma.task.count
        .mockResolvedValueOnce(1) // 张三
        .mockResolvedValueOnce(0) // 李四
        .mockResolvedValueOnce(6) // 王五

      const result = await (taskProcessorService as any).assignUserByWorkload(
        mockUsers,
        'assignedToId'
      )

      // 李四任务数最少（0个），应该被选中
      expect(result).toEqual(mockUsers[1])
      expect(result.name).toBe('李四')
    })

    it('应该轮流分配给可用用户（已达最小值但未达最大值）', async () => {
      // 模拟任务计数：张三5个，李四7个，王五8个
      mockPrisma.task.count
        .mockResolvedValueOnce(5) // 张三
        .mockResolvedValueOnce(7) // 李四
        .mockResolvedValueOnce(8) // 王五

      const result = await (taskProcessorService as any).assignUserByWorkload(
        mockUsers,
        'assignedToId'
      )

      // 张三任务数最少（5个），且未达到最大值8，应该被选中
      expect(result).toEqual(mockUsers[0])
      expect(result.name).toBe('张三')
    })

    it('应该在所有用户都达到最大值时轮流分配', async () => {
      // 模拟任务计数：张三8个，李四10个，王五12个（都达到最大值）
      mockPrisma.task.count
        .mockResolvedValueOnce(8) // 张三
        .mockResolvedValueOnce(10) // 李四
        .mockResolvedValueOnce(12) // 王五

      const result = await (taskProcessorService as any).assignUserByWorkload(
        mockUsers,
        'assignedToId'
      )

      // 张三任务数最少（8个），应该被选中
      expect(result).toEqual(mockUsers[0])
      expect(result.name).toBe('张三')
    })

    it('应该处理空用户列表', async () => {
      const result = await (taskProcessorService as any).assignUserByWorkload(
        [],
        'assignedToId'
      )

      expect(result).toBeNull()
    })

    it('应该处理null用户列表', async () => {
      const result = await (taskProcessorService as any).assignUserByWorkload(
        null,
        'assignedToId'
      )

      expect(result).toBeNull()
    })
  })

  describe('assignPersonnel', () => {
    it('应该基于项目分配人员', async () => {
      const projectId = 1
      const mockProjectUsers = [
        {
          user: {
            id: 1,
            name: '张三',
            email: '<EMAIL>',
            roles: [UserRole.Triage],
            dailyReportMin: 3,
            dailyReportMax: 8,
            isActive: true,
          },
        },
        {
          user: {
            id: 2,
            name: '李四',
            email: '<EMAIL>',
            roles: [UserRole.PM],
            dailyReportMin: 2,
            dailyReportMax: 10,
            isActive: true,
          },
        },
        {
          user: {
            id: 3,
            name: '王五',
            email: '<EMAIL>',
            roles: [UserRole.WM],
            dailyReportMin: 5,
            dailyReportMax: 12,
            isActive: true,
          },
        },
      ]

      mockPrisma.userProject.findMany.mockResolvedValue(mockProjectUsers)
      
      // 模拟每个用户的任务计数都为0
      mockPrisma.task.count.mockResolvedValue(0)

      const result = await (taskProcessorService as any).assignPersonnel(projectId)

      expect(mockPrisma.userProject.findMany).toHaveBeenCalledWith({
        where: { projectId },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              roles: true,
              dailyReportMin: true,
              dailyReportMax: true,
              isActive: true,
            },
          },
        },
      })

      expect(result.processor).toEqual(mockProjectUsers[0].user)
      expect(result.dataEntry).toEqual(mockProjectUsers[1].user)
      expect(result.qualityControl).toEqual(mockProjectUsers[2].user)
    })
  })
})
