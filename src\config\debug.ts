/**
 * 调试配置
 */

export const debugConfig = {
  // 是否启用调试模式
  enabled: process.env.NODE_ENV === 'development',
  
  // 调试API配置
  api: {
    // 是否启用调试API端点
    enableDebugEndpoints: process.env.ENABLE_DEBUG_API === 'true' || process.env.NODE_ENV === 'development',
    
    // API请求超时时间（毫秒）
    timeout: 30000,
    
    // 最大重试次数
    maxRetries: 3,
    
    // 重试延迟（毫秒）
    retryDelay: 1000
  },
  
  // 邮件处理调试配置
  emailProcessor: {
    // 是否启用详细日志
    verboseLogging: true,
    
    // 是否保存调试信息到任务元数据
    saveDebugInfo: true,
    
    // 是否跳过邮件通知（调试时可能不想发送真实邮件）
    skipEmailNotifications: process.env.DEBUG_SKIP_EMAIL_NOTIFICATIONS === 'true',
    
    // 默认任务优先级
    defaultPriority: 'MEDIUM' as const,
    
    // 是否启用AI分析
    enableAIAnalysis: process.env.DISABLE_AI_ANALYSIS !== 'true',
    
    // 是否启用项目匹配
    enableProjectMatching: process.env.DISABLE_PROJECT_MATCHING !== 'true'
  },
  
  // EWS连接配置
  ews: {
    // 连接超时时间（毫秒）
    connectionTimeout: 10000,
    
    // 是否启用EWS调试日志
    enableDebugLogging: process.env.EWS_DEBUG === 'true',
    
    // 最大邮件内容长度（字符）
    maxEmailContentLength: 10000
  },
  
  // 安全配置
  security: {
    // 是否允许在生产环境中使用调试功能
    allowInProduction: process.env.ALLOW_DEBUG_IN_PRODUCTION === 'true',
    
    // 调试API访问密钥（如果设置）
    accessKey: process.env.DEBUG_ACCESS_KEY,
    
    // 是否记录敏感信息（密码等）
    logSensitiveInfo: false
  },
  
  // 日志配置
  logging: {
    // 日志级别
    level: process.env.DEBUG_LOG_LEVEL || 'info',
    
    // 是否输出到控制台
    console: true,
    
    // 是否保存到文件
    file: false,
    
    // 日志文件路径
    filePath: './logs/debug.log'
  }
}

/**
 * 检查是否允许使用调试功能
 */
export function isDebugEnabled(): boolean {
  if (process.env.NODE_ENV === 'production' && !debugConfig.security.allowInProduction) {
    return false
  }
  
  return debugConfig.enabled
}

/**
 * 检查是否允许访问调试API
 */
export function isDebugApiEnabled(): boolean {
  if (!isDebugEnabled()) {
    return false
  }
  
  return debugConfig.api.enableDebugEndpoints
}

/**
 * 验证调试API访问权限
 */
export function validateDebugAccess(accessKey?: string): boolean {
  if (!isDebugApiEnabled()) {
    return false
  }
  
  // 如果设置了访问密钥，则需要验证
  if (debugConfig.security.accessKey) {
    return accessKey === debugConfig.security.accessKey
  }
  
  // 如果没有设置访问密钥，在开发环境中允许访问
  return process.env.NODE_ENV === 'development'
}

/**
 * 获取调试日志前缀
 */
export function getDebugLogPrefix(module: string): string {
  return `[DEBUG:${module}]`
}

/**
 * 调试日志函数
 */
export function debugLog(module: string, message: string, data?: any): void {
  if (!isDebugEnabled() || !debugConfig.logging.console) {
    return
  }
  
  const prefix = getDebugLogPrefix(module)
  
  if (data) {
    console.log(prefix, message, data)
  } else {
    console.log(prefix, message)
  }
}

/**
 * 调试错误日志函数
 */
export function debugError(module: string, message: string, error?: any): void {
  if (!isDebugEnabled() || !debugConfig.logging.console) {
    return
  }
  
  const prefix = getDebugLogPrefix(module)
  
  if (error) {
    console.error(prefix, message, error)
  } else {
    console.error(prefix, message)
  }
}

export default debugConfig
