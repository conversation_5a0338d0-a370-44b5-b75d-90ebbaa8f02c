'use client';

import React, { createContext, useContext, useEffect } from 'react';
import { RootStore } from './RootStore';

// 创建 Context
const StoreContext = createContext<RootStore | null>(null);

// 创建单例 store 实例
let rootStore: RootStore | null = null;

const getRootStore = (): RootStore => {
  if (!rootStore) {
    rootStore = new RootStore();
  }
  return rootStore;
};

// Provider 组件
interface StoreProviderProps {
  children: React.ReactNode;
}

export const StoreProvider: React.FC<StoreProviderProps> = ({ children }) => {
  const store = getRootStore();

  useEffect(() => {
    // 组件挂载时初始化
    const initializeStore = async () => {
      await store.officeStore.initializeOffice()
      // 监听 Office.js 准备状态
      if (store.officeStore.isOfficeReady) {
        store.initialize();
      }  
    };

    initializeStore();

    // 组件卸载时清理
    return () => {
      store.dispose();
    };
  }, [store]);

  return (
    <StoreContext.Provider value={store}>
      {children}
    </StoreContext.Provider>
  );
};

// Hook 用于获取 stores
export const useStores = (): RootStore => {
  const context = useContext(StoreContext);
  if (!context) {
    throw new Error('useStores must be used within a StoreProvider');
  }
  return context;
};

// 便捷的 hooks 用于获取特定的 store
export const useNotificationStore = () => useStores().notificationStore;
export const useOfficeStore = () => useStores().officeStore;
export const useAuthStore = () => useStores().authStore;
export const useProjectStore = () => useStores().projectStore;
export const useEmailStore = () => useStores().emailStore;
