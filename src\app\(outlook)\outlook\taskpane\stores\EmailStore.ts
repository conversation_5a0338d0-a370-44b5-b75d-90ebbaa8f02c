import { makeAutoObservable } from 'mobx';
import type { NotificationStore } from './NotificationStore';
import type { AuthStore } from './AuthStore';
import { convertToEwsId, getCurEmailDetails, buildMimeEmail } from '../helper'

// 完整的邮件信息类型
export interface FullEmailInfo {
  // Office.MessageRead的基本信息
  itemId: string;
  subject: string;
  sender: string;
  receivedTime: string;
  // getCurEmailDetails的完整信息
  from: string;
  to: string;
  cc: string;
  date: string;
  body: string;
  attachments: Array<{
    name: string;
    size: number;
    isInline: boolean;
    id: string;
    contentType: string;
    content: string; // base64
    encoding: string;
  }>;
}

export class EmailStore {
  selectedEmails: FullEmailInfo[] = [];
  isProcessing = false;
  isProcessingCurrent = false;
  currentEmailInfo: {
    itemId: string;
    subject: string;
    sender: string;
    receivedTime: string;
  } | null = null;

  private notificationStore: NotificationStore;
  private authStore: AuthStore | null = null;

  constructor(notificationStore: NotificationStore) {
    makeAutoObservable(this);
    this.notificationStore = notificationStore;
  }

  // 设置 AuthStore 引用（避免循环依赖）
  setAuthStore = (authStore: AuthStore) => {
    this.authStore = authStore;
  };

  // 检查选中的邮件
  checkSelectedEmails = async () => {
    try {
      const item = Office.context.mailbox.item;

      if (!item) {
        this.notificationStore.showWarning('请先选择一封邮件');
        return;
      }

      // 获取完整的邮件详细信息
      const emailDetails = await getCurEmailDetails();

      // 检查是否已经在选中列表中
      const isAlreadySelected = this.selectedEmails.some(email => email.itemId === item.itemId);

      if (isAlreadySelected) {
        this.notificationStore.showWarning('该邮件已经在选中列表中');
        return;
      }

      // 创建完整的邮件信息对象
      const fullEmailInfo: FullEmailInfo = {
        // Office.MessageRead的基本信息
        itemId: item.itemId,
        subject: item.subject || '',
        sender: item.from?.emailAddress || '未知发件人',
        receivedTime: item.dateTimeCreated?.toISOString() || '',
        // getCurEmailDetails的完整信息
        from: emailDetails.from,
        to: emailDetails.to,
        cc: emailDetails.cc,
        date: emailDetails.date,
        body: emailDetails.body as string,
        attachments: emailDetails.attachments,
      };

      // 添加到选中列表
      this.selectedEmails.push(fullEmailInfo);
      this.notificationStore.showSuccess(`已添加邮件: ${fullEmailInfo.subject}`);

    } catch (error) {
      console.error('检查选中邮件失败:', error);
      this.notificationStore.showError('检查选中邮件失败，请重试');
    }
  };

  // 清空所有选中的邮件
  handleClearAllEmails = async () => {
    if (this.selectedEmails.length === 0) {
      this.notificationStore.showWarning('没有选中的邮件需要清空');
      return;
    }

    const confirmed = await this.notificationStore.showConfirm(
      `确定要清空所有 ${this.selectedEmails.length} 封选中的邮件吗？`
    );
    
    if (confirmed) {
      this.selectedEmails = [];
      this.notificationStore.showSuccess('已清空所有选中的邮件');
    }
  };

  // 处理邮件（分发或处理）
  handleProcessEmails = async (projectId: string) => {
    const userInfo = this.authStore?.userInfo;
    if (!userInfo) {
      this.notificationStore.showError('用户未登录');
      return;
    }
   
  };

  // WM角色的批量分发功能
  handleDistribute = async () => {
    const userInfo = this.authStore?.userInfo;
    if (!userInfo) {
      this.notificationStore.showError('用户未登录');
      return;
    }

    if (!userInfo.roles.includes('WM')) {
      this.notificationStore.showError('只有WM用户可以使用分发功能');
      return;
    }

    if (this.selectedEmails.length === 0) {
      this.notificationStore.showWarning('请先选择要分发的邮件');
      return;
    }

    const confirmed = await this.notificationStore.showConfirm(
      `确定要分发 ${this.selectedEmails.length} 封邮件吗？`
    );

    if (!confirmed) return;

    this.isProcessing = true;
    try {
      const emailData = this.selectedEmails.map(email => ({
        itemId: email.itemId,
        sender: email.sender,
        receivedTime: email.receivedTime,
        subject: email.subject,
        // 添加完整的邮件信息
        from: email.from,
        to: email.to,
        cc: email.cc,
        date: email.date,
        body: email.body,
        attachments: email.attachments,
      }));

      const response = await fetch('/api/outlook/distribute-emails', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          emails: emailData, 
        }),
      });

      const result = await response.json();

      if (result.success) {
        this.notificationStore.showSuccess(
          `成功分发了 ${this.selectedEmails.length} 封邮件`
        );
        this.selectedEmails = [];
      } else {
        this.notificationStore.showError(result.error || '分发邮件失败');
      }
    } catch (error) {
      console.error('分发邮件失败:', error);
      this.notificationStore.showError('分发邮件失败，请重试');
    } finally {
      this.isProcessing = false;
    }
  };

  // 移除特定邮件
  removeEmail = (emailId: string) => {
    this.selectedEmails = this.selectedEmails.filter(email => email.itemId !== emailId);
  };

  // 清空选中邮件
  clearSelectedEmails = () => {
    this.selectedEmails = [];
  };

  // 获取选中邮件数量
  get selectedEmailsCount(): number {
    return this.selectedEmails.length;
  }
 
 
}

 